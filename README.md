<div align="center">

# 🧠 Productivity Dashboard

[![Live Demo](https://img.shields.io/badge/🌐_Live_Demo-Visit_Now-00adb5?style=for-the-badge)](https://yashaswirai.github.io/Cohort-DOM-project/)
[![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/HTML)
[![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=for-the-badge&logo=css3&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/CSS)
[![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](https://opensource.org/licenses/MIT)

*A sleek, interactive, and all-in-one productivity dashboard built with pure web technologies*

**Combine task management, time tracking, daily planning, motivation, and goal-setting in a single, beautiful interface**

[🚀 Live Demo](#-live-demo) • [✨ Features](#-key-features) • [🛠️ Installation](#️-installation--setup) • [📖 Usage](#-usage-guide) • [🤝 Contributing](#-contributing)

</div>

---

## 📋 Table of Contents

<details>
<summary>Click to expand</summary>

- [🌟 Live Demo](#-live-demo)
- [📸 Screenshots](#-screenshots)
- [✨ Key Features](#-key-features)
- [🏗️ Project Structure](#️-project-structure)
- [🛠️ Installation & Setup](#️-installation--setup)
- [📖 Usage Guide](#-usage-guide)
- [⚙️ How It Works](#️-how-it-works)
- [🎨 Themes](#-themes)
- [🔧 API Configuration](#-api-configuration)
- [📚 Technologies Used](#-technologies-used)
- [🚀 Future Improvements](#-future-improvements)
- [🤝 Contributing](#-contributing)
- [📝 License](#-license)
- [👨‍💻 Author](#-author)
- [🙏 Acknowledgments](#-acknowledgments)

</details>

---

## 🌟 Live Demo

<div align="center">

### 🔗 [**Experience the Dashboard Live**](https://yashaswirai.github.io/Cohort-DOM-project/)

*Click above to explore all features in your browser*

</div>



## ✨ Key Features

<div align="center">

| Feature | Description | Status |
|---------|-------------|--------|
| 📝 **Smart To-Do List** | Add, edit, delete tasks with importance markers | ✅ Active |
| 📆 **Daily Planner** | Hour-by-hour planning (5 AM - 11 PM) | ✅ Active |
| ⏱️ **Pomodoro Timer** | Customizable focus/break sessions | ✅ Active |
| ✨ **Daily Motivation** | Fresh inspirational quotes daily | ✅ Active |
| 🎯 **Goal Tracking** | Set and track personal objectives | ✅ Active |
| 🌍 **Live Weather** | Location-based weather updates | ✅ Active |
| 🎨 **Theme Switcher** | 4 beautiful color schemes | ✅ Active |
| � **Data Persistence** | All data saved locally | ✅ Active |

</div>

### 📝 Smart To-Do Management
- ➕ **Add Tasks**: Create tasks with titles, descriptions, and importance levels
- ✏️ **Edit Tasks**: Modify existing tasks inline
- 🗑️ **Delete Tasks**: Remove completed or unwanted tasks
- 💾 **Auto-Save**: All changes persist in browser storage
- 🔥 **Priority System**: Mark important tasks with visual indicators

### 📆 Comprehensive Daily Planner
- 🕐 **18-Hour Coverage**: Plan from 5:00 AM to 11:00 PM
- ⚡ **Real-time Saving**: Changes save automatically as you type
- 📱 **Responsive Design**: Works perfectly on all devices
- 🎯 **Time Blocking**: Organize your day hour by hour

### ⏱️ Advanced Pomodoro Timer
- ⏯️ **Full Control**: Start, pause, and reset functionality
- 🎛️ **Customizable**: Set your own focus and break durations
- 🔄 **Auto-Switch**: Seamlessly transitions between focus and break modes
- 🎨 **Visual Feedback**: Clear session indicators and progress display

### ✨ Daily Motivation System
- 🌅 **Fresh Content**: New inspirational quotes every session
- 🎭 **Curated Quality**: Powered by RealInspire API
- 👤 **Author Attribution**: Full credit to quote authors
- 🎨 **Beautiful Display**: Elegant typography and layout

### � Goal Tracking & Management
- ➕ **Quick Addition**: Add goals with timestamps
- ✅ **Progress Tracking**: Mark goals as complete
- 🗑️ **Easy Management**: Delete achieved or outdated goals
- ⏰ **Time Stamps**: Track when goals were set

### 🌍 Live Environmental Data
- 🕐 **Real-time Clock**: Updates every second
- 🌅 **Smart Greetings**: Changes based on time of day
- 🌤️ **Weather Integration**: Location-based weather via WeatherAPI
- 🎨 **Dynamic Backgrounds**: Wallpapers change with time of day

### 🎨 Beautiful Theme System
- 🌈 **4 Unique Themes**: Carefully crafted color palettes
- 🔄 **One-Click Switching**: Instant theme changes
- 💾 **Preference Memory**: Your theme choice is remembered
- 🎯 **Accessibility**: High contrast and readable combinations

---

## 🏗️ Project Structure

```
📁 PRODUCTIVITY DASHBOARD
├── 📄 index.html     # Main structure and HTML components
├── 📄 style.css      # Styling, themes, and responsive design
└── 📄 script.js      # Core functionality and feature logic
```

<details>
<summary>📑 Detailed Structure</summary>

### 📄 index.html
- Main HTML structure
- Card-based dashboard layout
- Modal templates for each feature
- Weather and time display components

### 📄 style.css
- CSS variables for theming
- Responsive design with Flexbox
- Animation and transition effects
- Modal and card styling

### 📄 script.js
- Feature implementations
- LocalStorage data management
- API integrations (Weather, Quotes)
- Event listeners and UI interactions

</details>

---

## ⚙️ How It Works

### 💡 Data Persistence
- **LocalStorage API**: All user data is stored in browser's localStorage
- **Automatic Saving**: Changes are saved instantly without manual intervention
- **Data Structure**: Organized JSON objects for todos, goals, and planner entries
- **Offline Support**: Works without internet connection (except for weather/quotes)

### 🖼️ Dynamic UI Architecture
- **Card System**: Dashboard uses card components to access features
- **Modal Interface**: Features open in full-screen modals for focused work
- **State Management**: JavaScript manages application state without frameworks
- **Event Delegation**: Efficient event handling for dynamic elements

### 🔄 API Integrations
- **Weather Data**: Location-based weather using WeatherAPI
- **Motivational Quotes**: Fresh content from RealInspire API
- **Error Handling**: Graceful fallbacks when APIs are unavailable

---

## �️ Installation & Setup

<details open>
<summary><b>Method 1: Quick Start (No Installation)</b></summary>

### 🌐 Use the Live Demo
1. Visit [https://yashaswirai.github.io/Cohort-DOM-project/](https://yashaswirai.github.io/Cohort-DOM-project/)
2. Start using all features immediately
3. Your data will be saved to your browser's localStorage

</details>

<details>
<summary><b>Method 2: Local Installation</b></summary>

### 1️⃣ Clone the Repository
```bash
git clone https://github.com/Yashaswirai/Cohort-DOM-project.git
cd Cohort-DOM-project
```

### 2️⃣ Open in Browser
Simply open `index.html` in any modern browser:

```bash
# Windows
start index.html

# macOS
open index.html

# Linux
xdg-open index.html
```

### 3️⃣ Development Setup (Optional)
For the best development experience:
1. Install [Visual Studio Code](https://code.visualstudio.com/)
2. Add the [Live Server extension](https://marketplace.visualstudio.com/items?itemName=ritwickdey.LiveServer)
3. Right-click on `index.html` and select "Open with Live Server"
4. The app will open and automatically reload on changes

</details>

---

## 📖 Usage Guide

<details>
<summary><b>🎯 Getting Started</b></summary>

1. **Dashboard Overview**: The main screen displays cards for each productivity tool
2. **Accessing Features**: Click any card to open its full-screen interface
3. **Switching Tools**: Close the current tool to return to the dashboard
4. **Changing Themes**: Click "Change Theme" in the top navigation bar

</details>

<details>
<summary><b>📝 Using the To-Do List</b></summary>

1. **Adding Tasks**: Enter a task title, description, and mark importance
2. **Editing Tasks**: Click the "Edit" button on any task to modify it
3. **Deleting Tasks**: Remove tasks with the "Delete" button
4. **Task Priority**: Important tasks are visually highlighted

</details>

<details>
<summary><b>⏱️ Using the Pomodoro Timer</b></summary>

1. **Starting a Session**: Click "Start" to begin the countdown
2. **Customizing Time**: Adjust minutes and seconds before starting
3. **Pausing/Resuming**: Use the "Pause" button to temporarily stop
4. **Resetting**: Click "Reset" to return to the default settings
5. **Auto-switching**: Timer automatically toggles between Focus and Break modes

</details>

<details>
<summary><b>📆 Using the Daily Planner</b></summary>

1. **Hourly Planning**: Enter activities for each hour slot
2. **Auto-saving**: Your entries save automatically as you type
3. **Time Coverage**: Plan your day from 5:00 AM to 11:00 PM

</details>

---

## 🎨 Themes

The dashboard offers four carefully designed themes that transform the entire interface:

| Theme | Primary Color | Secondary Color | Text Color | Accent Color |
|-------|--------------|----------------|-----------|-------------|
| **Default** | #222831 | #393e46 | #00adb5 | #eeeeee |
| **Soft Lavender** | #FFF2E0 | #C0C9EE | #A2AADB | #898AC4 |
| **Ocean Breeze** | #E3FDFD | #CBF1F5 | #A6E3E9 | #71C9CE |
| **Night Mode** | #090040 | #2E294E | #8661C1 | #EFBCD5 |

*Click the "Change Theme" button in the navigation bar to cycle through themes*

---

## 🔧 API Configuration

<details>
<summary><b>Weather API Setup</b></summary>

The dashboard uses [WeatherAPI](https://www.weatherapi.com/) for location-based weather:

1. The API key is already configured in the application
2. Weather data updates automatically based on your location
3. Requires location permission in your browser
4. No additional setup required for users

</details>

<details>
<summary><b>Quotes API</b></summary>

Motivational quotes are fetched from [RealInspire API](https://api.realinspire.live/):

1. No API key required
2. Quotes refresh automatically when the motivation section is opened
3. No additional setup required for users

</details>

---

## 📚 Technologies Used

<div align="center">

| Technology | Purpose | Documentation |
|------------|---------|---------------|
| ![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=flat&logo=html5&logoColor=white) | Structure | [MDN Docs](https://developer.mozilla.org/en-US/docs/Web/HTML) |
| ![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=flat&logo=css3&logoColor=white) | Styling | [MDN Docs](https://developer.mozilla.org/en-US/docs/Web/CSS) |
| ![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black) | Functionality | [MDN Docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript) |
| ![LocalStorage](https://img.shields.io/badge/LocalStorage-4285F4?style=flat&logo=google-chrome&logoColor=white) | Data Storage | [MDN Docs](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage) |
| ![WeatherAPI](https://img.shields.io/badge/WeatherAPI-2ECCAA?style=flat&logo=openweathermap&logoColor=white) | Weather Data | [API Docs](https://www.weatherapi.com/docs/) |
| ![RealInspire](https://img.shields.io/badge/RealInspire-FF6B6B?style=flat&logo=quotefancy&logoColor=white) | Quotes | [API Endpoint](https://api.realinspire.live/v1/quotes/random) |

</div>

### Core Technologies
- **HTML5**: Semantic structure and accessibility
- **CSS3**: Custom properties, Flexbox layout, animations
- **JavaScript (ES6+)**: Modern JS with async/await, template literals
- **Web Storage API**: LocalStorage for data persistence

### External APIs
- **WeatherAPI**: Real-time weather information
- **RealInspire API**: Motivational quotes

### Development Tools
- **VS Code**: Recommended editor with Live Server
- **Chrome DevTools**: For testing and debugging

---

## 🚀 Future Improvements

<div align="center">

| Feature | Description | Priority |
|---------|-------------|----------|
| 🔐 **User Authentication** | Account creation and profile management | High |
| 📅 **Calendar Integration** | Monthly/weekly view with event planning | Medium |
| ☁️ **Cloud Sync** | Data synchronization across devices | High |
| 🔔 **Notifications** | Sound alerts and browser notifications | Medium |
| 🌓 **Dark/Light Toggle** | Direct toggle instead of theme cycling | Low |
| 📊 **Productivity Analytics** | Stats and insights on app usage | Medium |
| 📱 **PWA Support** | Install as a Progressive Web App | High |
| 🔄 **Data Export/Import** | Backup and restore functionality | Low |

</div>

---

## 🤝 Contributing

Contributions are what make the open-source community such an amazing place to learn, inspire, and create. Any contributions you make are **greatly appreciated**.

<details>
<summary><b>How to Contribute</b></summary>

### 1. Fork the Project
* Click the Fork button in the top right of the repository

### 2. Create your Feature Branch
```bash
git checkout -b feature/AmazingFeature
```

### 3. Commit your Changes
```bash
git commit -m 'Add some AmazingFeature'
```

### 4. Push to the Branch
```bash
git push origin feature/AmazingFeature
```

### 5. Open a Pull Request
* Go back to your fork on GitHub and open a pull request

</details>

### Contribution Ideas
- Bug fixes and issue resolution
- New productivity features
- UI/UX improvements
- Documentation enhancements
- Performance optimizations

---

## 👨‍� Author

<div align="center">

### 👨‍💻 **Yashaswi**

*B.Tech in Computer Science & Engineering (Data Science Specialization)*

[![LinkedIn](https://img.shields.io/badge/LinkedIn-0077B5?style=for-the-badge&logo=linkedin&logoColor=white)](https://www.linkedin.com/in/yashaswi-rai-real/)
[![Portfolio](https://img.shields.io/badge/Portfolio-00adb5?style=for-the-badge&logo=About.me&logoColor=white)](https://yashaswirai.github.io/Web_Resume/)

</div>

---

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## � Acknowledgments

- Inspiration from various productivity methodologies
- Weather data provided by [WeatherAPI](https://www.weatherapi.com/)
- Quotes provided by [RealInspire](https://api.realinspire.live/)
- Icons and images from [Unsplash](https://unsplash.com/)

---

<div align="center">

### ⭐ Star this repo if you found it helpful! ⭐

*Made with ❤️ by [Yashaswi](https://yashaswi.dev)*

</div>

