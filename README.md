# 🧠 Productivity Dashboard

> A sleek, interactive, and all-in-one productivity dashboard built using **HTML**, **CSS**, and **Vanilla JavaScript**. It combines task management, time tracking, daily planning, motivation, and goal-setting into a single browser interface.

---

## 🌟 Live Demo

🔗 [Click to explore](https://vanilla-desktop-os.onrender.com)

---

## 📁 Project Structure

```
├── index.html        # Main structure of the dashboard UI
├── style.css         # Styling for all UI components and theming
└── script.js         # Logic for dynamic features (timer, todos, theme, weather)
```

---

## 📌 Key Features

### 📝 To-Do List

- Add tasks with title, description, and importance marker
- Edit or delete tasks
- Tasks persist in `localStorage`

### 📆 Daily Planner

- Plan your day by the hour (5:00 AM – 11:00 PM)
- Inputs auto-save to `localStorage`

### ⏱️ Pomodoro Timer

- Start, pause, and reset timers
- Customize focus and break durations
- Toggle between **Focus** and **Break** modes automatically

### ✨ Daily Motivation

- Fetches inspirational quotes using `https://api.realinspire.live/v1/quotes/random`
- Clean display with author name

### 🌟 Daily Goals

- Set and track personal goals
- Mark as complete or delete
- Auto-save with timestamps

### 🌍 Live Date, Time & Weather

- Real-time clock updates every second
- Dynamic greetings based on time of day
- Auto-fetch location-based weather using WeatherAPI

### 🎨 Theme Switcher

- Toggle between **4 vibrant themes** with a single button

---

## ⚙️ How It Works

### 💡 Data Persistence

- Uses `localStorage` to store todos, daily goals, and planner data
- Ensures data is preserved on page reload

### 🖼️ Dynamic UI Behavior

- Cards open modals for each app section
- Close buttons hide sections without page reload
- JavaScript handles state updates for each widget

---

## 📸 Screenshots

> *(Include some screenshots here if hosting on GitHub or a portfolio)*

---

## 🛠️ Installation & Run Locally

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/productivity-dashboard.git
cd productivity-dashboard
```

### 2. Open in Browser

Simply open `index.html` in any modern browser:

```bash
# Windows
start index.html

# macOS/Linux
open index.html
```

### 3. (Optional) Host Locally

Use the Live Server extension in VS Code for best experience.

---

## 📚 Technologies Used

- HTML5
- CSS3 (Custom Properties, Flexbox, Animations)
- JavaScript (ES6)
- OpenWeather API
- RealInspire Quotes API
- LocalStorage

---

## 🚀 Future Improvements

- Add authentication & user profiles
- Integrate a calendar view for better planning
- Cloud sync (e.g., Firebase)
- Sound notifications for timer
- Dark/light mode toggle instead of theme cycling

---

## 🙌 Author

👨‍💻 **Yashaswi**\
B.Tech in Computer Science & Engineering (Data Science Specialization)

> Connect with me on [LinkedIn](https://www.linkedin.com/in/yashaswi-yourprofile)\
> Portfolio: [yashaswi.dev](https://yashaswi.dev)

---

## 📝 License

This project is licensed under the MIT License.

---

## 📬 Feedback & Contributions

Pull requests, issues, and suggestions are welcome!

> ⭐ Star the repo if you found this helpful!

