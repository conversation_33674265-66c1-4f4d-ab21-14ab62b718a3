@import url("https://fonts.googleapis.com/css2?family=Tiro+Devanagari+Hindi:ital@0;1&family=Tomorrow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:root {
  --primary-color: #222831;
  --secondary-color: #393e46;
  --text-color: #00adb5;
  --accent-color: #eeeeee;
  --font-family: "Tomorrow", sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  width: 100%;
  font-family: var(--font-family);
  color: var(--secondary-color);
  background-color: var(--primary-color);
  font-size: clamp(0.8rem, 1vw, 1rem);
}
main {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 1rem;
}
nav{
  width: 100%;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0 2rem;
  border-radius: 0.5rem;
  background-color: #00adb5;
}
nav h1 {
  font-size: 2rem;
  color: var(--primary-color);
  font-weight: 800;
  text-transform: uppercase;
}
nav h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  color: var(--text-color);
  background-color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}
nav h2:active {
  transform: scale(0.97);
  transition: all 0.1s ease-in-out;
}
nav h2::selection{
  background-color: none;
  color: var(--text-color);
}
header{
  width: 100%;
  height: 18rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background: url("https://images.unsplash.com/photo-1549138144-42ff3cdd2bf8?q=80&w=1152&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
header .dateTime{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  color: var(--text-color);
}
header .dateTime h1 {
  font-size: 2.5rem;
  font-weight: bold;
}
header .dateTime h2 {
  font-size: 1.5rem;
  font-weight: 700;
}
header .dateTime h3 {
  font-size: 1.2rem;
  font-weight: 700;
}
header .weather {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--text-color);
}
header .weather img {
  width: 5rem;
  height: 5rem;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
}
.weather-info {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}
.dateTime-info {
  width: 100%;
  display: flex;
}
section.cards {
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: space-around;
}
.card {
  width: 18rem;
  height: 25rem;
  background-color: var(--accent-color);
  border: 1px solid var(--secondary-color);
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
}
.card:active{
  transform: scale(0.97);
  transition: all 0.2s ease-in-out;
}
.card h2{
  position: absolute;
  bottom: 3%;
  right: 3%;
  color: var(--primary-color);
}
.card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5rem 0.5rem 0 0;
}

.details {
  position: fixed; 
  width: 100vw; 
  height: 100vh; 
  top: 0;
  left: 0;
  background-color: var(--secondary-color);
  padding: 1rem;
  display: none;
  transition: all 0.3s ease-in-out;
  z-index: 9999;
  overflow-y: auto;
}
.details .clsBtn{
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: var(--primary-color);
  color: red;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.3rem;
  cursor: pointer;
}
.details .clsBtn:active{
  scale: 0.97;
  transition: all 0.1s ease-in-out;
}
.to-do-list {
  width: 100%;
  height: 100%;
}
.to-do-list h3 {
  text-align: center;
  font-size: 2.5rem;
  text-decoration: underline;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}
.todo-Container {
  width: 100%;
  height: 85%;
  display: flex;
  justify-content: space-between;
}
.create-todo{
  width: 30%;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 1rem;
}
.create-todo form{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  justify-content: space-around;
  /* gap: 1.5rem; */
}
.create-todo form input[type="text"] {
  width: 100%;
  border: none;
  outline: none;
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-family: var(--font-family);
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 1.5rem;
}
.create-todo form textarea{
  width: 100%;
  height: 50%;
  border: none;
  outline: none;
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-family: var(--font-family);
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 1.3rem;
}
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
}
.checkbox-container input[type="checkbox"] {
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
}
.checkbox-container label {
  cursor: pointer;
  font-size: 1.2rem;
}
.create-todo form button {
  width: 100%;
  padding: 1rem;
  background-color: var(--accent-color);
  color: var(--primary-color);
  font-family: var(--font-family);
  font-weight: bold;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.5rem;
  cursor: pointer;
}
.lists {
  width: 65%;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 1rem;
  padding: 1rem;
  overflow-y: auto;
}
.lists ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.lists ul li {
  width: 100%;
  height: 4rem;
  background-color: var(--secondary-color);
  color: var(--text-color);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}
.lists ul li input{
  width: 80%;
  background-color: transparent;
  padding: 0.5rem;
  color: var(--text-color);
  border: none;
  outline: none;
  font-size: 1.2rem;
  font-family: var(--font-family);
}
.lists ul li .imp-true{
  color: var(--accent-color);
  background-color: var(--text-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.3rem;
  font-weight: bold;
  position: absolute;
  top: -10%;
  left: 0;
}
.lists ul li .imp-false{
  display: none;
}
.lists ul li button {
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.3rem;
  cursor: pointer;
}
.lists ul li .edit{
  background-color: #FFCC00;
  color: #471396;
  font-weight: bold;
}
.lists ul li .delete {
  background-color: red;
  color: #fff;
  font-weight: bold;
}

.daily-planner {
  width: 100%;
  height: 100%;
}
.daily-planner h2 {
  text-align: center;
  font-size: 2.5rem;
  text-decoration: underline;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  text-decoration: none;
}
.daily-planner .schedules {
  width: 100%;
  height: 85%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: var(--primary-color);
  overflow-y: auto;
  flex-wrap: wrap;
  padding: 1rem;
  border-radius: 1rem;
  gap: 2.5rem;
}
.schedules .plan {
  width: 45%;
  height: 3.5rem;
  background-color: var(--secondary-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 0.5rem;
  position: relative;
}
.plan h5 {
  position: absolute;
  top: 0%;
  left: 2%;
  transform: translateY(-50%);
  color: var(--text-color);
  font-size: 1rem;
} 
.plan input[type="text"] {
  width: 100%;
  height: 100%;
  background-color: transparent;
  color: var(--text-color);
  border: none;
  outline: none;
  font-size: 1.4rem;
  font-family: var(--font-family);
  padding-left: 1rem;
}

.motivation-page {
  width: 100%;
  height: 100%;
}
.motivation-page h2 {
  text-align: center;
  font-size: 2.5rem;
  text-decoration: underline;
  margin-bottom: 1.5rem;
  color: var(--accent-color);
}
.motivation-container {
  /* width: 80%;
  height: 60%; */
  box-shadow: 0 0 10rem var(--text-color);
  /* border: 5px solid var(--text-color); */
  border-radius: 1rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  padding: 1.5rem;
}
.motivation-container h1 {
  width: 100%;
  text-align: center;
  background-color: var(--primary-color);
  padding: 1rem 0;
  border-radius: 1rem;
  color: var(--text-color);
  font-size: 1.8rem;
  margin-bottom: 2rem;
}
.motivation{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.motivation p {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}
p.author {
  font-weight: 500;
  font-size: 1.5rem;
  background-color: var(--primary-color);
  padding: 0.5rem 1rem;
  color: var(--text-color);
  font-style: italic;
  border-radius: 0.5rem;
  margin-top: 1rem;
}
.timer-page{
  width: 100%;
  height: 100%;
}
.timer-page h2 {
  text-align: center;
  font-size: 2.8rem;
  text-decoration: underline;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}
.timer-container{
  width: 100%;
  height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.timer{
  width: 30rem;
  height: 30rem;
  background-color: var(--accent-color);
  border: 1rem solid var(--primary-color);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  gap: 2rem;
}
.timer h1{
  font-size: 2.5rem;
  color: var(--text-color);
  text-align: center;
  font-weight: bold;
  font-family: var(--font-family);
}
.timer .time-display {
  padding: 1rem 2rem;
  border: 5px solid var(--primary-color);
  font-size: 5rem;
  color: var(--primary-color);
  font-weight: bold;
  font-family: var(--font-family);
  display: flex;
  align-items: center;
  justify-content: center;
}
.time-display{
  width: 20rem;
  height: 10rem;
  background-color: var(--secondary-color);
  border-radius: 1rem;
  color: var(--text-color);
}
.time-display .time {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.time-display .time  input[type="number"] {
  width: 6rem;
  height: 5rem;
  /* background-color: transparent; */
  color: var(--text-color);
  border: none;
  outline: none;
  font-size: 3rem;
  text-align: center;
  font-family: var(--font-family);
  border-radius: 1rem;
}
.time-display .time input[type="number"]::-webkit-inner-spin-button,
.time-display .time input[type="number"]::-webkit-outer-spin-button{
  -webkit-appearance: none;
}
.time-display .time label {
  display: block;
  font-size: 1.5rem;
  color: var(--text-color);
  font-family: var(--font-family);
}
.time-display .time span {
  font-size: 6rem;
  padding-bottom: 1.5rem;
  color: var(--text-color);
  font-family: var(--font-family);
}
.timer .controls {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  border-radius: 0.5rem;
}
.controls button {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.5rem;
  cursor: pointer;
}
.daily-goals {
  width: 100%;
  height: 100%;
}
.daily-goals h2 {
  text-align: center;
  font-size: 2.5rem;
  text-decoration: underline;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}
.goals-container {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background-color: var(--primary-color);
  border-radius: 1rem;
  padding: 1rem;
}
.goals-container .goal-form {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  position: relative;
}
.goal-form input[type="text"] {
  width: 100%;
  padding: 0.5rem;
  border: none;
  outline: none;
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-size: 1.5rem;
  border-radius: 0.5rem;
}
.goal-form button{
  position: absolute;
  right: 0;
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 1.5rem;
  cursor: pointer;
}
.goal-form button:active{
  transform: scale(0.97);
  transition: all 0.1s ease-in-out;
}
.goals {
  width: 100%;
  height: 80%;
  overflow-y: auto;
  margin-top: 1rem;
}
.goals h3{
  margin: 1rem 0;
  font-size: 1.5rem;
  color: var(--text-color);
}
.goal{
  width: 100%;
  height: 3rem;
  background-color: var(--secondary-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 0 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  position: relative;
}
.goal input[type="checkbox"] {
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
}
.goal input[type="checkbox"]:checked + label {
  text-decoration: line-through;
  color: var(--accent-color);
}
.goal .rightEnd{
  position: absolute;
  right: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}
.goal .rightEnd button:active{
  transform: scale(0.97);
  transition: all 0.1s ease-in-out;
}
.goal .rightEnd .delete-btn {
  background-color: red;
  color: #fff;
  font-weight: bold;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
}
.goal .rightEnd h5{
  color: var(--text-color);
  font-size: 1.2rem;
  font-weight: bold;
  background-color: var(--primary-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.3rem;
}
.goal-list {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.goal-list h1 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  text-decoration: underline;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}